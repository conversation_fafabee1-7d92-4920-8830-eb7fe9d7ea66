# Dev.to Blog Upload API Documentation

## Overview
This API allows users to upload, update, and retrieve blog articles on Dev.to platform through your application.

## Prerequisites
1. User must have a Dev.to account
2. User must generate a Dev.to API key from their Dev.to settings
3. User must connect their Dev.to account using the `/api/devto-connect/` endpoint

## Authentication
All endpoints require JWT authentication via the `Authorization` header and a `brand` header.

## Endpoints

### 1. Connect Dev.to Account
**Endpoint:** `POST /api/devto-connect/`

**Headers:**
```
Authorization: Bearer <jwt_token>
brand: <brand_id>
Content-Type: application/json
```

**Request Body:**
```json
{
    "api_key": "your_devto_api_key_here"
}
```

**Response:**
```json
{
    "status": true,
    "message": "Dev.to connected successfully",
    "Connect": true,
    "user_info": {
        "id": 123456,
        "username": "your_username",
        "name": "Your Name",
        "profile_image": "https://dev-to-uploads.s3.amazonaws.com/..."
    }
}
```

### 2. Upload Blog to Dev.to
**Endpoint:** `POST /api/devto-blog-upload/`

**Headers:**
```
Authorization: Bearer <jwt_token>
brand: <brand_id>
Content-Type: application/json
```

**Request Body:**
```json
{
    "title": "Your Blog Title",
    "body_markdown": "# Your Blog Content\n\nThis is your blog content in markdown format.",
    "published": true,
    "tags": ["javascript", "webdev", "tutorial", "beginners"],
    "description": "A brief description of your blog post",
    "main_image": "https://example.com/your-cover-image.jpg",
    "canonical_url": "https://yourblog.com/original-post",
    "series": "Your Series Name"
}
```

**Alternative with HTML:**
```json
{
    "title": "Your Blog Title",
    "body_html": "<h1>Your Blog Content</h1><p>This is your blog content in HTML format.</p>",
    "published": true,
    "tags": ["javascript", "webdev", "tutorial", "beginners"]
}
```

**Required Fields:**
- `title`: String (required)
- `body_markdown` OR `body_html`: String (one is required)

**Optional Fields:**
- `published`: Boolean (default: true)
- `tags`: Array of strings (max 4 tags)
- `description`: String
- `main_image`: String (URL)
- `canonical_url`: String (URL)
- `series`: String

**Response:**
```json
{
    "status": true,
    "message": "Blog uploaded to Dev.to successfully",
    "article_data": {
        "id": 123456,
        "title": "Your Blog Title",
        "url": "https://dev.to/username/your-blog-title-123",
        "published": true,
        "published_at": "2024-01-15T10:30:00Z",
        "tags": ["javascript", "webdev", "tutorial", "beginners"]
    }
}
```

### 3. Update Blog on Dev.to
**Endpoint:** `PUT /api/devto-blog-update/`

**Headers:**
```
Authorization: Bearer <jwt_token>
brand: <brand_id>
Content-Type: application/json
```

**Request Body:**
```json
{
    "article_id": 123456,
    "title": "Updated Blog Title",
    "body_markdown": "# Updated Content\n\nThis is your updated blog content.",
    "published": false,
    "tags": ["javascript", "webdev"]
}
```

**Required Fields:**
- `article_id`: Integer (required)

**Optional Fields:** (only include fields you want to update)
- `title`: String
- `body_markdown` OR `body_html`: String
- `published`: Boolean
- `tags`: Array of strings (max 4 tags)
- `description`: String
- `main_image`: String (URL)
- `canonical_url`: String (URL)
- `series`: String

**Response:**
```json
{
    "status": true,
    "message": "Blog updated on Dev.to successfully",
    "article_data": {
        "id": 123456,
        "title": "Updated Blog Title",
        "url": "https://dev.to/username/updated-blog-title-123",
        "published": false,
        "published_at": null,
        "tags": ["javascript", "webdev"]
    }
}
```

### 4. Get Blog from Dev.to
**Endpoint:** `GET /api/devto-blog-get/?article_id=123456`

**Headers:**
```
Authorization: Bearer <jwt_token>
brand: <brand_id>
```

**Query Parameters:**
- `article_id`: Integer (required)

**Response:**
```json
{
    "status": true,
    "message": "Article retrieved successfully",
    "article_data": {
        "id": 123456,
        "title": "Your Blog Title",
        "description": "A brief description",
        "url": "https://dev.to/username/your-blog-title-123",
        "published": true,
        "published_at": "2024-01-15T10:30:00Z",
        "tags": ["javascript", "webdev", "tutorial", "beginners"],
        "body_markdown": "# Your Blog Content\n\nThis is your blog content.",
        "canonical_url": "https://yourblog.com/original-post",
        "cover_image": "https://example.com/cover.jpg",
        "social_image": "https://example.com/social.jpg",
        "reading_time_minutes": 5,
        "public_reactions_count": 25,
        "comments_count": 3,
        "page_views_count": 150
    }
}
```

## Error Responses

### Dev.to Not Connected
```json
{
    "status": false,
    "message": "Dev.to is not connected. Please connect your Dev.to account first."
}
```

### Invalid API Key
```json
{
    "status": false,
    "message": "Invalid Dev.to API key"
}
```

### Missing Required Fields
```json
{
    "status": false,
    "message": "Title is required"
}
```

### Dev.to API Error
```json
{
    "status": false,
    "message": "Failed to upload blog to Dev.to: Error creating article: 422 - Validation failed"
}
```

## Usage Examples

### Complete Workflow

1. **Connect Dev.to Account:**
```bash
curl -X POST "https://your-api.com/api/devto-connect/" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "brand: your_brand_id" \
  -H "Content-Type: application/json" \
  -d '{"api_key": "your_devto_api_key"}'
```

2. **Upload a Blog:**
```bash
curl -X POST "https://your-api.com/api/devto-blog-upload/" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "brand: your_brand_id" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Getting Started with JavaScript",
    "body_markdown": "# Introduction\n\nJavaScript is a powerful programming language...",
    "published": true,
    "tags": ["javascript", "beginners", "tutorial"],
    "description": "A beginner-friendly guide to JavaScript"
  }'
```

3. **Update the Blog:**
```bash
curl -X PUT "https://your-api.com/api/devto-blog-update/" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "brand: your_brand_id" \
  -H "Content-Type: application/json" \
  -d '{
    "article_id": 123456,
    "title": "Getting Started with JavaScript - Updated",
    "published": false
  }'
```

4. **Retrieve the Blog:**
```bash
curl -X GET "https://your-api.com/api/devto-blog-get/?article_id=123456" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "brand: your_brand_id"
```

## Notes

1. **Tags Limitation:** Dev.to allows maximum 4 tags per article
2. **Markdown Support:** Dev.to primarily uses Markdown format
3. **HTML Conversion:** If you provide HTML, it will be converted to Markdown automatically
4. **API Rate Limits:** Dev.to has rate limits, so avoid making too many requests in a short time
5. **Authentication:** Make sure your Dev.to API key has the necessary permissions

## Getting Dev.to API Key

1. Go to [Dev.to Settings](https://dev.to/settings/account)
2. Scroll down to "DEV Community API Keys"
3. Generate a new API key
4. Copy the key and use it in the connect endpoint
